# -*- coding: utf-8 -*-
import datetime
import json
import os.path
import time


class JsonWriterPipeline(object):
    """
    写入json文件的pipline
    """

    def __init__(self):
        self.file = None
        if not os.path.exists('../output'):
            os.mkdir('../output')

    def process_item(self, item, spider):
        """
        处理item
        """
        if not self.file:
            now = datetime.datetime.now()
            if spider.name == "repost":
                file_name = spider.tweet_id + '_repost.jsonl'
            elif spider.name == "tweet_by_tweet_id":
                file_name = spider.tweet_id + '_tweet.jsonl'
            elif spider.name == "comment":
                file_name = spider.tweet_id + '_comment.jsonl'
            else:
                file_name = spider.name + "_" + now.strftime("%Y%m%d%H%M%S") + '.jsonl'
            self.file = open(f'../output/{file_name}', 'wt', encoding='utf-8')
            print(f"writing crawled data to ../output/{file_name}")
        item['crawl_time'] = int(time.time())
        line = json.dumps(dict(item), ensure_ascii=False) + "\n"
        self.file.write(line)
        self.file.flush()
        return item
